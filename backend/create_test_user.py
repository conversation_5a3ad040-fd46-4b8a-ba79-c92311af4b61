#!/usr/bin/env python3
"""
Simple script to create a test user and some gamification data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.models.user import User, UserRole
from app.models.gamification import PointsTransaction
from datetime import datetime, timezone
import random

def create_test_user_and_data():
    """Create a test user and some gamification data"""
    db = SessionLocal()
    
    try:
        print("Creating test user and data...")
        
        # Check if we already have a test user
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if not test_user:
            # Create a test student user
            test_user = User(
                email="<EMAIL>",
                hashed_password="$2b$12$dummy_hash",  # Dummy hash
                full_name="Test Student",
                role=UserRole.STUDENT,
                is_active=True,
                email_verified=True,
                profile_completed=True
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
            print(f"✓ Created test user: {test_user.email}")
        else:
            print(f"✓ Test user already exists: {test_user.email}")
        
        # Create some test points transactions
        existing_points = db.query(PointsTransaction).filter(
            PointsTransaction.user_id == test_user.id
        ).count()
        
        if existing_points == 0:
            # Create some test transactions
            transactions = [
                {"points": 50, "description": "Welcome bonus", "type": "bonus"},
                {"points": 25, "description": "Completed first quiz", "type": "quiz"},
                {"points": 30, "description": "Daily practice streak", "type": "streak"},
                {"points": 40, "description": "Perfect score achievement", "type": "achievement"},
                {"points": 20, "description": "Course enrollment", "type": "course"},
            ]
            
            for i, trans_data in enumerate(transactions):
                transaction = PointsTransaction(
                    user_id=test_user.id,
                    points=trans_data["points"],
                    description=trans_data["description"],
                    transaction_type=trans_data["type"],
                    created_at=datetime.now(timezone.utc) - timezone.timedelta(hours=i)
                )
                db.add(transaction)
            
            db.commit()
            print(f"✓ Created {len(transactions)} test transactions")
        else:
            print(f"✓ Test user already has {existing_points} transactions")
        
        # Check total points
        total_points = db.query(PointsTransaction).filter(
            PointsTransaction.user_id == test_user.id
        ).with_entities(db.func.sum(PointsTransaction.points)).scalar() or 0
        
        print(f"✓ Test user total points: {total_points}")
        
        # Test the gamification service
        from app.services import gamification_service
        
        profile = gamification_service.get_user_profile(db, test_user.id)
        print(f"✓ User profile: Level {profile['current_level']['level_number']}, {profile['total_points']} points")
        
        leaderboard = gamification_service.get_leaderboard(db, limit=5)
        print(f"✓ Leaderboard has {len(leaderboard)} entries")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    print("=== Creating Test User and Data ===")
    success = create_test_user_and_data()
    
    if success:
        print("\n🎉 Test user and data created successfully!")
        print("You can now test the dashboard with:")
        print("Email: <EMAIL>")
        print("Password: (any password - authentication is bypassed for testing)")
    else:
        print("\n❌ Failed to create test data")
        sys.exit(1)
