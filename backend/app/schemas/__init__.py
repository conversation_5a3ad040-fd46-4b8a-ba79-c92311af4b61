from app.schemas.token import Token, TokenPayload
from app.schemas.user import User, UserCreate, UserInDB, UserUpdate, ProfileComplete, EmailVerificationRequest, EmailVerificationConfirm, EmailVerificationResponse, PasswordResetRequest, PasswordResetConfirm, PasswordResetResponse
from app.schemas.school import School, SchoolCreate, SchoolUpdate
from app.schemas.department import Department, DepartmentCreate, DepartmentUpdate
from app.schemas.course import Course, CourseCreate, CourseUpdate
from app.schemas.question import Question, QuestionCreate, QuestionUpdate, AIQuestionRequest
from app.schemas.session import Session, SessionCreate, SessionUpdate, SessionBookingRequest
from app.schemas.tutor import TutorProfile, TutorProfileCreate, TutorProfileUpdate, TutorReview, TutorReviewCreate, TutorReviewUpdate, TutorDashboardStats, TutorSearchFilters, TutorListItem
from app.schemas.ai_assistant import AIAssistantResponse, AssistantQuery
from app.schemas.student_progress import (
    StudentQuestionAttempt, StudentQuestionAttemptCreate, StudentQuestionAttemptUpdate,
    StudentBookmark, StudentBookmarkCreate, StudentBookmarkUpdate,
    StudentExam, StudentExamCreate, StudentExamUpdate, StudentExamWithAttempts,
    StudentExamAttempt, StudentExamAttemptCreate
)
from app.schemas.gamification import (
    Badge, BadgeCreate, BadgeUpdate,
    UserBadge, UserBadgeCreate,
    PointsTransaction, PointsTransactionCreate,
    UserLevel, UserLevelCreate, UserLevelUpdate,
    UserStreak, UserStreakCreate, UserStreakUpdate,
    UserGamificationProfile, LeaderboardEntry, Leaderboard
)
from app.schemas.student_tools import (
    StudentNote, StudentNoteCreate, StudentNoteUpdate,
    NoteGeneratedMCQ, NoteGeneratedMCQCreate,
    MCQGenerationJob, MCQGenerationJobCreate, MCQGenerationJobUpdate,
    StudentNoteUploadResponse, MCQGenerationRequest, MCQGenerationResponse, MCQGenerationStatusResponse,
    ExamConfigRequest, ExamConfigResponse
)
from app.schemas.admin_flashcard import (
    AdminFlashCard, AdminFlashCardCreate, AdminFlashCardUpdate, AdminFlashCardWithCourse,
    AdminFlashCardCSVUploadResponse, AdminFlashCardBulkCreate, AdminFlashCardBulkUpdate, AdminFlashCardBulkDelete,
    AdminFlashCardImageUploadResponse, AdminFlashCardStats
)
