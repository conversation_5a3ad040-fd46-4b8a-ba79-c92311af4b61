from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional

from app.db.base_class import Base
from app.core.utils import utc_now


class AdminFlashCard(Base):
    """Model for admin-managed flashcards"""
    __tablename__ = "admin_flashcard"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    course_id: Mapped[int] = mapped_column(ForeignKey("course.id"))
    front_content: Mapped[str] = mapped_column(Text)
    back_content: Mapped[str] = mapped_column(Text)
    topic: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    difficulty: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # easy, medium, hard
    media_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)  # For images
    is_active: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=True)
    created_by: Mapped[int] = mapped_column(ForeignKey("user.id"))  # Admin who created it
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    course = relationship("Course", back_populates="admin_flashcards")
    creator = relationship("User", foreign_keys=[created_by])
